use backtest::framework::BacktestFramework;
use backtest::websocket::handler::{BinanceOrderParams, BinanceOrderRequest};
use serde_json;
use std::time::Duration;
use tokio::time::sleep;
use tokio_tungstenite::{connect_async, tungstenite::Message};
use tracing::{info, warn};

#[tokio::test]
async fn test_order_flow_integration() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("Starting order flow integration test");

    // 设置测试配置文件
    std::env::set_var("BACKTEST_CONFIG", "test_config.toml");

    // 创建并启动框架
    let mut framework = BacktestFramework::new()
        .await
        .expect("Failed to create framework");

    // 初始化组件
    framework
        .initialize_components()
        .await
        .expect("Failed to initialize components");
    framework
        .initialize_data_pipeline()
        .await
        .expect("Failed to initialize data pipeline");

    // 启动框架（在后台）
    let framework_handle = tokio::spawn(async move {
        if let Err(e) = framework.prepare().await {
            warn!("Framework error: {}", e);
        } else {
            info!("Framework prepared successfully");
        }
    });

    // 等待服务器启动
    info!("Waiting for servers to start...");
    sleep(Duration::from_millis(3000)).await;
    info!("Attempting to connect to WebSocket server...");

    // 连接到WebSocket服务器（使用正确的端口）
    let ws_url = "ws://127.0.0.1:8082";
    let (ws_stream, _) = connect_async(ws_url)
        .await
        .expect("Failed to connect to WebSocket");
    let (mut ws_sender, mut ws_receiver) = ws_stream.split();

    info!("Connected to WebSocket server");

    // 创建订单请求
    let order_params = BinanceOrderParams {
        api_key: None,
        symbol: "BTCUSDT".to_string(),
        side: "BUY".to_string(),
        order_type: "LIMIT".to_string(),
        position_side: Some("BOTH".to_string()),
        time_in_force: Some("GTC".to_string()),
        quantity: Some("1.0".to_string()),
        price: Some("50000.0".to_string()),
        new_client_order_id: None,
        reduce_only: None,
        stop_price: None,
        working_type: None,
        price_protect: None,
        self_trade_prevention_mode: None,
        timestamp: None,
        signature: None,
        recv_window: None,
    };

    let order_request = BinanceOrderRequest {
        id: Some(serde_json::Value::Number(serde_json::Number::from(1))),
        method: "order.place".to_string(),
        params: order_params,
    };

    // 发送订单
    let order_json = serde_json::to_string(&order_request).expect("Failed to serialize order");
    info!("Sending order: {}", order_json);

    use futures::SinkExt;
    ws_sender
        .send(Message::Text(order_json))
        .await
        .expect("Failed to send order");

    // 等待响应
    use futures::StreamExt;
    let mut response_count = 0;
    let max_responses = 3; // 期望收到订单确认和可能的状态更新

    while response_count < max_responses {
        tokio::select! {
            message = ws_receiver.next() => {
                match message {
                    Some(Ok(Message::Text(text))) => {
                        info!("Received response: {}", text);
                        response_count += 1;

                        // 检查是否是订单响应
                        if text.contains("orderId") || text.contains("result") {
                            info!("✓ Received order response");
                        }
                    }
                    Some(Ok(Message::Close(_))) => {
                        info!("WebSocket connection closed");
                        break;
                    }
                    Some(Err(e)) => {
                        warn!("WebSocket error: {}", e);
                        break;
                    }
                    None => {
                        info!("WebSocket stream ended");
                        break;
                    }
                    _ => {}
                }
            }
            _ = sleep(Duration::from_secs(5)) => {
                info!("Timeout waiting for responses");
                break;
            }
        }
    }

    info!("Test completed, received {} responses", response_count);

    // 清理
    framework_handle.abort();

    // 验证至少收到了一个响应
    assert!(
        response_count > 0,
        "Should have received at least one response"
    );

    info!("✓ Order flow integration test passed");
}
