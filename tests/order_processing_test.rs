use backtest::matching::MatchingEngine;
use backtest::types::{Order, OrderSide, OrderStatus, OrderType, Price};
use backtest::websocket::handler::{BinanceOrderParams, WebSocketHandler};
use backtest::websocket::subscription::SubscriptionManager;
use chrono::Utc;
use std::sync::Arc;
use tokio::sync::{broadcast, mpsc};
use tracing::info;

#[tokio::test]
async fn test_order_processing_unit() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("Starting order processing unit test");

    // 创建通道
    let (_market_data_tx, market_data_rx) = broadcast::channel(100);
    let (order_tx, order_rx) = mpsc::channel(100);
    let (trade_tx, mut trade_rx) = broadcast::channel(100);
    let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

    // 创建撮合引擎
    let mut matching_engine = MatchingEngine::new(
        market_data_rx,
        order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
    );

    // 启动撮合引擎（在后台）
    let engine_handle = tokio::spawn(async move {
        if let Err(e) = matching_engine.start().await {
            eprintln!("Matching engine error: {}", e);
        }
    });

    // 创建WebSocket handler
    let subscription_manager = Arc::new(SubscriptionManager::new());
    let (message_tx, mut message_rx) = mpsc::channel(100);
    let client_id = subscription_manager.add_client(message_tx.clone());
    let handler = WebSocketHandler::new(
        client_id,
        subscription_manager,
        message_tx,
        Some(order_tx.clone()),
    );

    // 创建测试订单参数
    let order_params = BinanceOrderParams {
        api_key: None,
        symbol: "BTCUSDT".to_string(),
        side: "BUY".to_string(),
        order_type: "LIMIT".to_string(),
        position_side: Some("BOTH".to_string()),
        time_in_force: Some("GTC".to_string()),
        quantity: Some("1.0".to_string()),
        price: Some("50000.0".to_string()),
        new_client_order_id: None,
        reduce_only: None,
        stop_price: None,
        working_type: None,
        price_protect: None,
        self_trade_prevention_mode: None,
        timestamp: None,
        signature: None,
        recv_window: None,
    };

    // 测试订单转换
    let order = handler
        .convert_binance_order_to_internal(&order_params)
        .unwrap();
    info!("Created order: {:?}", order);

    // 验证订单字段
    assert_eq!(order.side, OrderSide::Buy);
    assert_eq!(order.order_type, OrderType::Limit);
    assert_eq!(order.price, Some(Price::new(50000.0)));
    assert_eq!(order.quantity, 1.0);
    assert_eq!(order.status, OrderStatus::Pending);

    // 发送订单到撮合引擎
    info!("Sending order to matching engine: {:?}", order);
    order_tx.send(order.clone()).await.unwrap();
    info!("Order sent to matching engine successfully");

    // 等待一小段时间让matching engine处理订单
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 等待订单更新
    tokio::select! {
        order_update = order_update_rx.recv() => {
            match order_update {
                Ok(updated_order) => {
                    info!("Received order update: {:?}", updated_order);
                    // 验证订单状态已更新（限价单在没有对手盘时可能被取消）
                    assert!(
                        updated_order.status == OrderStatus::Cancelled ||
                        updated_order.status == OrderStatus::Filled ||
                        updated_order.status == OrderStatus::PartiallyFilled,
                        "Order status should be updated from Pending, got: {:?}", updated_order.status
                    );
                }
                Err(e) => {
                    panic!("Failed to receive order update: {}", e);
                }
            }
        }
        _ = tokio::time::sleep(tokio::time::Duration::from_secs(5)) => {
            panic!("Timeout waiting for order update");
        }
    }

    // 清理
    engine_handle.abort();

    info!("✓ Order processing unit test passed");
}

#[tokio::test]
async fn test_market_order_processing() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("Starting market order processing test");

    // 创建通道
    let (_market_data_tx, market_data_rx) = broadcast::channel(100);
    let (order_tx, order_rx) = mpsc::channel(100);
    let (trade_tx, mut trade_rx) = broadcast::channel(100);
    let (order_update_tx, mut order_update_rx) = broadcast::channel(100);
    let (market_data_forward_tx, _market_data_forward_rx) = broadcast::channel(100);

    // 创建撮合引擎
    let mut matching_engine = MatchingEngine::new(
        market_data_rx,
        order_rx,
        trade_tx,
        order_update_tx,
        market_data_forward_tx,
    );

    // 启动撮合引擎（在后台）
    let engine_handle = tokio::spawn(async move {
        if let Err(e) = matching_engine.start().await {
            eprintln!("Matching engine error: {}", e);
        }
    });

    // 创建市价单
    let market_order = Order {
        id: "market_test_1".to_string(),
        order_type: OrderType::Market,
        side: OrderSide::Buy,
        price: None,
        quantity: 1.0,
        status: OrderStatus::Pending,
        timestamp: Utc::now(),
    };

    // 发送市价单
    order_tx.send(market_order).await.unwrap();
    info!("Market order sent to matching engine");

    // 等待订单更新
    tokio::select! {
        order_update = order_update_rx.recv() => {
            match order_update {
                Ok(updated_order) => {
                    info!("Received market order update: {:?}", updated_order);
                    // 市价单应该立即被处理（即使没有对手盘）
                    assert!(updated_order.status != OrderStatus::Pending);
                }
                Err(e) => {
                    panic!("Failed to receive market order update: {}", e);
                }
            }
        }
        _ = tokio::time::sleep(tokio::time::Duration::from_secs(2)) => {
            panic!("Timeout waiting for market order update");
        }
    }

    // 清理
    engine_handle.abort();

    info!("✓ Market order processing test passed");
}
