use backtest::websocket::handler::{Binance<PERSON>rder<PERSON>arams, WebSocketHandler};
use backtest::websocket::subscription::SubscriptionManager;
use backtest::types::{OrderSide, OrderStatus, OrderType, Price};
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::info;

#[tokio::test]
async fn test_order_conversion() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("Starting order conversion test");

    // 创建WebSocket handler
    let subscription_manager = Arc::new(SubscriptionManager::new());
    let (message_tx, _message_rx) = mpsc::channel(100);
    let client_id = subscription_manager.add_client(message_tx.clone());
    let handler = WebSocketHandler::new(
        client_id,
        subscription_manager,
        message_tx,
        None, // 不需要order_tx用于转换测试
    );

    // 创建测试订单参数
    let order_params = BinanceOrderParams {
        api_key: None,
        symbol: "BTCUSDT".to_string(),
        side: "BUY".to_string(),
        order_type: "LIMIT".to_string(),
        position_side: Some("BOTH".to_string()),
        time_in_force: Some("GTC".to_string()),
        quantity: Some("1.0".to_string()),
        price: Some("50000.0".to_string()),
        new_client_order_id: None,
        reduce_only: None,
        stop_price: None,
        working_type: None,
        price_protect: None,
        self_trade_prevention_mode: None,
        timestamp: None,
        signature: None,
        recv_window: None,
    };

    // 测试订单转换
    let order = handler.convert_binance_order_to_internal(&order_params).unwrap();
    info!("Converted order: {:?}", order);

    // 验证订单字段
    assert_eq!(order.side, OrderSide::Buy);
    assert_eq!(order.order_type, OrderType::Limit);
    assert_eq!(order.price, Some(Price::new(50000.0)));
    assert_eq!(order.quantity, 1.0);
    assert_eq!(order.status, OrderStatus::Pending);

    info!("✓ Order conversion test passed");
}

#[tokio::test]
async fn test_market_order_conversion() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("Starting market order conversion test");

    // 创建WebSocket handler
    let subscription_manager = Arc::new(SubscriptionManager::new());
    let (message_tx, _message_rx) = mpsc::channel(100);
    let client_id = subscription_manager.add_client(message_tx.clone());
    let handler = WebSocketHandler::new(
        client_id,
        subscription_manager,
        message_tx,
        None,
    );

    // 创建市价单参数
    let order_params = BinanceOrderParams {
        api_key: None,
        symbol: "BTCUSDT".to_string(),
        side: "SELL".to_string(),
        order_type: "MARKET".to_string(),
        position_side: Some("BOTH".to_string()),
        time_in_force: Some("GTC".to_string()),
        quantity: Some("0.5".to_string()),
        price: None, // 市价单没有价格
        new_client_order_id: None,
        reduce_only: None,
        stop_price: None,
        working_type: None,
        price_protect: None,
        self_trade_prevention_mode: None,
        timestamp: None,
        signature: None,
        recv_window: None,
    };

    // 测试市价单转换
    let order = handler.convert_binance_order_to_internal(&order_params).unwrap();
    info!("Converted market order: {:?}", order);

    // 验证市价单字段
    assert_eq!(order.side, OrderSide::Sell);
    assert_eq!(order.order_type, OrderType::Market);
    assert_eq!(order.price, None); // 市价单没有价格
    assert_eq!(order.quantity, 0.5);
    assert_eq!(order.status, OrderStatus::Pending);

    info!("✓ Market order conversion test passed");
}

#[tokio::test]
async fn test_order_channel_integration() {
    // 初始化日志
    tracing_subscriber::fmt::init();

    info!("Starting order channel integration test");

    // 创建订单通道
    let (order_tx, mut order_rx) = mpsc::channel(100);

    // 创建WebSocket handler
    let subscription_manager = Arc::new(SubscriptionManager::new());
    let (message_tx, _message_rx) = mpsc::channel(100);
    let client_id = subscription_manager.add_client(message_tx.clone());
    let handler = WebSocketHandler::new(
        client_id,
        subscription_manager,
        message_tx,
        Some(order_tx),
    );

    // 创建订单参数
    let order_params = BinanceOrderParams {
        api_key: None,
        symbol: "BTCUSDT".to_string(),
        side: "BUY".to_string(),
        order_type: "LIMIT".to_string(),
        position_side: Some("BOTH".to_string()),
        time_in_force: Some("GTC".to_string()),
        quantity: Some("2.0".to_string()),
        price: Some("45000.0".to_string()),
        new_client_order_id: None,
        reduce_only: None,
        stop_price: None,
        working_type: None,
        price_protect: None,
        self_trade_prevention_mode: None,
        timestamp: None,
        signature: None,
        recv_window: None,
    };

    // 转换并发送订单
    let order = handler.convert_binance_order_to_internal(&order_params).unwrap();
    info!("Sending order through channel: {:?}", order);
    
    // 模拟WebSocket handler发送订单的过程
    if let Some(order_tx) = handler.order_tx.as_ref() {
        order_tx.send(order.clone()).await.unwrap();
        info!("Order sent successfully");
    }

    // 接收订单
    let received_order = order_rx.recv().await.unwrap();
    info!("Received order: {:?}", received_order);

    // 验证接收到的订单
    assert_eq!(received_order.id, order.id);
    assert_eq!(received_order.side, OrderSide::Buy);
    assert_eq!(received_order.order_type, OrderType::Limit);
    assert_eq!(received_order.price, Some(Price::new(45000.0)));
    assert_eq!(received_order.quantity, 2.0);

    info!("✓ Order channel integration test passed");
}
